from .constants import MIN_PASSWORD_LENGTH

__all__ = ['errors']

errors = {
    'Exception': {
        'ok': False,
        'error': 'SERVER_ERROR',
        'message': "Something went wrong while processing this request",
        'status': 500,
    },
    'InternalServerException': {
        'ok': False,
        'error': 'SERVER_ERROR',
        'message': "CloudAudit service is down. Please try again after some time!",
        'status': 500
    },
    'BadRequestException': {
        'ok': False,
        'error': 'BAD_REQUEST',
        'message': "Bad request error!",
        'status': 400
    },
    'ResourceNotFoundException': {
        'ok': False,
        'error': 'RESOURCE_NOT_FOUND',
        'message': "Requested resource not found error!",
        'status': 404
    },
    'ResourceNotFoundOrInsufficientPermissionsException': {
        'ok': False,
        'error': 'FORBIDDEN',
        'message': "You do not have sufficient permissions to access this resource",
        'status': 403
    },
    'InvalidEmailException': {
        'ok': False,
        'error': 'UNPROCESSABLE_ENTITY',
        'message': "Enter valid email address",
        'status': 422
    },
    'InvalidPasswordException': {
        'ok': False,
        'error': 'UNPROCESSABLE_ENTITY',
        'message': f"Password must be at least {MIN_PASSWORD_LENGTH} characters long",
        'status': 422
    },
    'UserExistsException': {
        'ok': False,
        'error': 'CONFLICT',
        'message': "User already exists",
        'status': 409
    },
    'AdminUserNotDeletedException': {
        'ok': False,
        'error': 'BAD_REQUEST',
        'message': "Admin user cannot be deleted",
        'status': 400
    },
    'InvalidCredentialsException': {
        'ok': False,
        'error': 'UNAUTHORIZED',
        'message': "Invalid Credentials",
        'status': 401
    },
    'EmailNotVerifiedException': {
        'ok': False,
        'error': 'EMAIL_NOT_VERIFIED',
        'message': "Please verify your email address to continue",
        'status': 401
    },
    'AccountExistsException': {
        'ok': False,
        'error': 'CONFLICT',
        'message': "Account already exists",
        'status': 409
    },
    'InvalidAWSCredentialsException': {
        'ok': False,
        'error': 'UNAUTHORIZED',
        'message': "Invalid AWS Credentials",
        'status': 401
    },
    'InvalidGCPCredentialsException': {
        'ok': False,
        'error': 'UNAUTHORIZED',
        'message': "Invalid GCP Credentials",
        'status': 401
    },
    'InvalidAzureCredentialsException': {
        'ok': False,
        'error': 'UNAUTHORIZED',
        'message': "Invalid Azure Credentials",
        'status': 401
    },
    'MaxScanAccountsException': {
        'ok': False,
        'error': 'TOO_MANY_REQUESTS',
        'message': "You have reached the maximum limit of running scans",
        'status': 429
    },
    'ServiceRequiredException': {
        'ok': False,
        'error': 'UNPROCESSABLE_ENTITY',
        'message': "Service is required for the scan",
        'status': 422
    },
    'ServiceNotValidException': {
        'ok': False,
        'error': 'UNPROCESSABLE_ENTITY',
        'message': "Provided services are not valid",
        'status': 422
    },
    'NoServicesAvailableForScanException': {
        'ok': False,
        'error': 'NOT_FOUND',
        'message': "No services available for scan",
        'status': 404
    },
    'ScanNotFoundException': {
        'ok': False,
        'error': 'NOT_FOUND',
        'message': "No services available for scan",
        'status': 404
    },
    'ScanNotAuthorizedException': {
        'ok': False,
        'error': 'FORBIDDEN',
        'message': "You not authorised to access the scan",
        'status': 403
    },
    'InvalidCloudProviderNameException': {
        'ok': False,
        'error': 'UNPROCESSABLE_ENTITY',
        'message': "Invalid cloud provider name",
        'status': 422
    },
    'NoAWSRegionException': {
        'ok': False,
        'error': 'UNPROCESSABLE_ENTITY',
        'message': "No AWS regions provided",
        'status': 422
    },
    'InvalidAWSRegionException': {
        'ok': False,
        'error': 'UNPROCESSABLE_ENTITY',
        'message': "Invalid AWS regions",
        'status': 422
    },
    'ScanAlreadyRunningException': {
        'ok': False,
        'error': 'CONFLICT',
        'message': "Scan for this account is already running",
        'status': 409
    },
    'AWSChildAccountAccessRoleException': {
        'ok': False,
        'error': 'NOT_FOUND',
        'message': "AWS child account access role (OrganizationAccountAccessRole) not found",
        'status': 404
    },
    'InsufficientPermissionsException': {
        'ok': False,
        'error': 'FORBIDDEN',
        'message': "You do not have sufficient permissions to perform this action",
        'status': 403
    },
    'InvalidPermissionException': {
        'ok': False,
        'error': 'UNPROCESSABLE_ENTITY',
        'message': "Requested permission is not valid",
        'status': 422
    },
    'CustomRoleNotFoundException': {
        'ok': False,
        'error': 'NOT_FOUND',
        'message': "Custom role not found",
        'status': 404
    },
    'WorkspaceNameRequiredException': {
        'ok': False,
        'error': 'UNPROCESSABLE_ENTITY',
        'message': "Workspace name is required",
        'status': 422
    },
    'RemediationInProgressException': {
        'ok': False,
        'error': 'CONFLICT',
        'message': "Remediation already in progress for this finding detail",
        'status': 409
    },
    'RemediateDetailNotValidException': {
        'ok': False,
        'error': 'BAD_REQUEST',
        'message': "Remediation detail is not valid",
        'status': 400
    },
    'CurrentPasswordNotValidException': {
        'ok': False,
        'error': 'BAD_REQUEST',
        'message': "Current password is not valid",
        'status': 400
    },
    'AdminUserPasswordNotChangedException': {
        'ok': False,
        'error': 'BAD_REQUEST',
        'message': "Admin user password cannot be changed",
        'status': 400
    },
    'AdminUserInfoNotUpdatedException': {
        'ok': False,
        'error': 'BAD_REQUEST',
        'message': "Admin user info cannot be updated",
        'status': 400
    },
    'CannotUpdateYourselfException': {
        'ok': False,
        'error': 'BAD_REQUEST',
        'message': "Cannot update yourself",
        'status': 400
    },
    'NotWorkspaceOwnerException': {
        'ok': False,
        'error': 'FORBIDDEN',
        'message': "Only workspace owner can transfer ownership",
        'status': 403
    },
    'CannotTransferToSelfException': {
        'ok': False,
        'error': 'BAD_REQUEST',
        'message': "Cannot transfer workspace ownership to yourself",
        'status': 400
    },
    'TargetUserNotInWorkspaceException': {
        'ok': False,
        'error': 'BAD_REQUEST',
        'message': "Target user is not in the same workspace",
        'status': 400
    },
    'TargetUserNotAdminException': {
        'ok': False,
        'error': 'BAD_REQUEST',
        'message': "Target user must have admin privileges to become workspace owner",
        'status': 400
    }
}

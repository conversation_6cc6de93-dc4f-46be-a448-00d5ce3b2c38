from typing import Dict, Any, List
from datetime import datetime, timezone
from app.common import SeverityEnum, ResourceComplianceStatusEnum, RDSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.rds.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "rds_instance_ssl_certificate_expiration": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_SSL_CERTIFICATE_EXPIRATION.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            instances = (cached.get("instances") or {}).get("DBInstances", [])
            certificates = (cached.get("certificates") or {}).get("Certificates", [])

            # Create a mapping of certificate identifiers to certificate details
            cert_map = {}
            for cert in certificates:
                cert_identifier = cert.get("CertificateIdentifier", "")
                cert_map[cert_identifier] = cert

            # Check RDS instances
            for instance in instances:
                db_instance_id = instance.get("DBInstanceIdentifier", "")
                engine = instance.get("Engine", "")
                engine_version = instance.get("EngineVersion", "")
                ca_certificate_identifier = instance.get("CACertificateIdentifier", "")

                # Analyze certificate status
                # Default response for missing certificate
                if not ca_certificate_identifier:
                    cert_analysis = {
                        "status": "no_certificate",
                        "valid_till": None,
                        "is_valid": False,
                    }
                else:
                    # Check if certificate exists in our mapping
                    cert_details = cert_map.get(ca_certificate_identifier)
                    if not cert_details:
                        cert_analysis = {
                            "status": "certificate_not_found",
                            "days_until_expiration": None,
                            "is_valid": False,
                        }
                    else:
                        # Extract certificate information
                        cert_type = cert_details.get("CertificateType", "")
                        valid_from = cert_details.get("ValidFrom")
                        valid_till = cert_details.get("ValidTill")

                        # Calculate expiration status
                        current_time = datetime.now(timezone.utc)
                        is_expired = False
                        days_until_expiration = None

                        if valid_till:
                            # Handle both string and datetime objects
                            if isinstance(valid_till, str):
                                try:
                                    valid_till = datetime.fromisoformat(valid_till.replace('Z', '+00:00'))
                                except ValueError:
                                    # If parsing fails, assume certificate is invalid
                                    cert_analysis = {
                                        "status": "invalid_date_format",
                                        "valid_till": valid_till,
                                        "is_valid": False,
                                    }
                                    continue

                            # Ensure valid_till is timezone-aware
                            if valid_till.tzinfo is None:
                                valid_till = valid_till.replace(tzinfo=timezone.utc)

                            time_diff = valid_till - current_time
                            days_until_expiration = time_diff.days
                            is_expired = days_until_expiration < 0

                        # Determine overall validity
                        # Certificate is valid if it's not expired, not deprecated, and not expiring soon (within 30 days)
                        is_valid = (not is_expired and
                                   days_until_expiration is not None and
                                   days_until_expiration > 30)

                        # Generate status
                        if is_expired:
                            status = "expired"
                        elif days_until_expiration is not None and days_until_expiration <= 30:
                            status = "expiring_soon"
                        elif days_until_expiration is not None and days_until_expiration <= 90:
                            status = "expiring_within_90_days"
                        else:
                            status = "valid"

                        cert_analysis = {
                            "status": status,
                            "valid_till": valid_till.isoformat() if valid_till else None,
                            "days_until_expiration": days_until_expiration,
                            "is_valid": is_valid,
                        }

                # Update overall compliance status
                if (findings["rds_instance_ssl_certificate_expiration"]["status"] == ResourceComplianceStatusEnum.PASS.value
                        and not cert_analysis["is_valid"]):
                    findings["rds_instance_ssl_certificate_expiration"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                # Add finding details
                findings["rds_instance_ssl_certificate_expiration"]["details"].append({
                    "resource_type": "DB Instance",
                    "db_instance_id": db_instance_id,
                    "engine": engine,
                    "engine_version": engine_version,
                    "region": region,
                    "ca_certificate_identifier": ca_certificate_identifier,
                    "certificate_valid_till": cert_analysis["valid_till"],
                    "compliance": cert_analysis["is_valid"],
                })

        return findings



    def remediate(self):
        pass

import importlib
from celery.utils.log import get_task_logger
from .celery import celery_obj
from celery import group, chord
from app.core.models.mysql import (get_account_credentials, save_findings, check_scan_completion,
                                   update_scan, update_scan_service,
                                   get_findings_for_scan_service_policy_check, update_findings,
                                   mysql_connection_pool_factory, sync_scan_services, get_check_detail,
                                   tracking_bulk_insert, tracking_set_status)
from app.core.models.mysql.mysql_mgr import get_shared_mysql_pool, get_connection_stats
from importlib import import_module
from app.common.enums import ScanStatusEnum, ScanServiceStatusEnum
import re

# Import retry tasks to register them with Celery
from app.core.services.celery_conf import retry_tasks

__all__ = ['get_findings', 'scan_service_task', 'execute_check_task', 'send_otp_email_task', 'send_verification_email_task', 'cleanup_expired_otps_task', 'cleanup_expired_verification_tokens_task', 'send_password_reset_email_task', 'cleanup_expired_password_reset_tokens_task']
# __all__ = ['get_findings', 'scan_service_task', 'execute_check_task']


logger = get_task_logger(__name__)


async def get_findings(credentials, cloud_provider_name, service_name, regions):
    module = importlib.import_module(f'app.core.services.api_cloud_providers.{cloud_provider_name}.'
                                     f'{service_name}.scan')
    service_class = getattr(module, 'ChecksProcessor')
    return await service_class(credentials, regions).run_checks()


async def save_or_update_findings(mysql_pool, scan_id, service_id, findings):
    for policy_check, finding_detail in findings.items():
        # check if finding already exists
        result = await get_findings_for_scan_service_policy_check(mysql_pool, scan_id, service_id, policy_check)
        if result:
            # update existing finding
            await update_findings(mysql_pool, result['id'], finding_detail)
        else:
            # insert new finding
            await save_findings(mysql_pool, scan_id, service_id, policy_check, finding_detail)


@celery_obj.task
def scan_service_task(scan_id, service, payload):
    import asyncio
    loop = asyncio.get_event_loop()
    # loop = asyncio.new_event_loop()
    # asyncio.set_event_loop(loop)

    async def run_task():
        # if mysql_pool is None:
        #     await init_mysql_pool()

        mysql_pool = await get_shared_mysql_pool()

        service_id = service["id"]
        service_name = service["name"]
        scan_service_id = await sync_scan_services(mysql_pool, scan_id, service_id, ScanServiceStatusEnum.RUNNING.value)

        credentials = await get_account_credentials(mysql_pool, payload["account_id"])

        # no separate scan_request persistence; regions will be stored per-check in tracking
        regions = payload.get("regions", [])
        if not regions:
            try:
                # On-the-fly region discovery if regions are missing
                from app.core.services.resource_explorer import ResourceExplorerService
                rex = ResourceExplorerService(credentials)
                service_region_map = await rex.get_service_region_mapping([{ "name": service_name }])
                regions = service_region_map.get(service_name, [])
                # Persist back to payload for downstream calls
                payload["regions"] = regions
                logger.info("Discovered regions for scan_id=%s service=%s: %s", scan_id, service_name.upper(), ", ".join(regions) if regions else "<none>")
            except Exception as discover_err:
                logger.warning("Region discovery fallback failed for service %s: %s", service_name.upper(), discover_err)
        # Normalize regions: drop falsy/duplicates and known invalid values like 'global'; enforce pattern
        region_pattern = re.compile(r"^[a-z]{2}-[a-z]+-\d$")
        regions = [r.strip() for r in regions if isinstance(r, str)]
        regions = [r for r in regions if r and r.lower() != 'global' and region_pattern.match(r)]
        regions = sorted(set(regions))
        logger.info("Regions for scan_id=%s service=%s: %s", scan_id, service_name.upper(), ", ".join(regions) if regions else "<none>")

        # If still empty, skip this service gracefully to avoid global endpoint calls
        if not regions:
            logger.warning("No valid regions resolved for scan_id=%s service=%s, skipping data fetch and checks", scan_id, service_name.upper())
            await update_scan_service(conn_pool=mysql_pool, scan_service_id=scan_service_id, update_last_scanned_at=True, status=ScanServiceStatusEnum.COMPLETED.value if hasattr(ScanServiceStatusEnum, 'COMPLETED') else ScanServiceStatusEnum.FAILED.value)
            is_scan_completed = await check_scan_completion(mysql_pool, scan_id)
            if is_scan_completed["status_check"]:
                await update_scan(mysql_pool, scan_id, ScanStatusEnum.COMPLETED.value, update_end=True)
            return

        # Phase 1: Data fetch and cache (service-agnostic)
        data_fetch_success = True
        fetch_error_message = None
        
        try:
            fetch_mod = import_module(
                f"app.core.services.api_cloud_providers.{payload['cloud_provider_name']}.{service_name}.data_fetch"
            )
            session_factory = fetch_mod.prepare_session_factory(credentials, regions)
            # Clean existing cache for this workspace/account/service before new scan
            try:
                if credentials.get("workspace_id") and credentials.get("aws_account_id"):
                    fetch_mod.clear_cache(credentials.get("workspace_id"), credentials.get("aws_account_id"))
            except Exception as clear_err:
                logger.warning("Cache clear failed for service %s: %s", service_name.upper(), clear_err)
            await fetch_mod.fetch_and_cache_all_regions(
                regions=regions,
                session_factory=session_factory,
                account_id=str(payload.get("account_id")),
                credentials=credentials
            )
            logger.info("Data fetch for service %s completed successfully", service_name.upper())
        except Exception as fetch_err:
            data_fetch_success = False
            fetch_error_message = str(fetch_err)
            logger.exception("Data fetch for service %s failed: %s", service_name.upper(), fetch_err)

        # Fetch enabled checks for this service
        checks = await get_check_detail(mysql_pool, service_id)
        logger.info("Prepared %d checks for service %s", len(checks or []), service_name.upper())
        check_paths = [c["script_path"] for c in checks] if checks else []

        # Insert tracking entries for all checks
        rows = await tracking_bulk_insert(
            mysql_pool,
            scan_id,
            service_id,
            checks,
            max_retries=payload.get("max_retries", 3),
            regions=regions,
        )
        logger.info("Inserted %d tracking entries for service %s", rows, service_name.upper())

        # If data fetch failed, mark all checks as failed in tracking table
        if not data_fetch_success and checks:
            logger.warning("Data fetch failed for service %s, marking all checks as failed", service_name.upper())
            for check in checks:
                try:
                    await tracking_set_status(
                        mysql_pool, 
                        scan_id, 
                        service_id, 
                        check["id"], 
                        "failed", 
                        error_message=f"Data fetch failed: {fetch_error_message}",
                        inc_retry=False,
                        mark_dlq=False
                    )
                    logger.info("Marked check %s as failed due to data fetch error", check["id"])
                except Exception as track_err:
                    logger.error("Failed to update tracking status for check %s: %s", check["id"], track_err)
            
            # Schedule retry for data fetch
            from app.core.services.celery_conf.retry_tasks import retry_failed_data_fetch
            retry_failed_data_fetch.apply_async(
                args=[scan_id, service_id, payload.get("max_retries", 3)],
                countdown=60  # Retry after 1 minute
            )
            logger.info("Scheduled retry for failed data fetch: scan %s, service %s", scan_id, service_id)

        # If data fetch failed, skip check execution and mark service as failed
        if not data_fetch_success:
            logger.warning("Skipping check execution for service %s due to data fetch failure", service_name.upper())
            # Mark service as failed
            await update_scan_service(
                conn_pool=mysql_pool, 
                scan_service_id=scan_service_id, 
                update_last_scanned_at=True,
                status=ScanServiceStatusEnum.FAILED.value
            )
            # Check if scan is completed (all services done)
            is_scan_completed = await check_scan_completion(mysql_pool, scan_id)
            if is_scan_completed["status_check"]:
                await update_scan(mysql_pool, scan_id, ScanStatusEnum.COMPLETED.value, update_end=True)
        elif not check_paths:
            # Fallback to legacy single-file scan implementation
            findings = await get_findings(credentials, payload["cloud_provider_name"], service_name, payload["regions"])
            await save_or_update_findings(mysql_pool, scan_id, service_id, findings)

            # update each scan service status to completed
            await update_scan_service(conn_pool=mysql_pool, scan_service_id=scan_service_id, update_last_scanned_at=True)
            # mark parent scan as completed if all services are done
            is_scan_completed = await check_scan_completion(mysql_pool, scan_id)
            if is_scan_completed["status_check"]:
                await update_scan(mysql_pool, scan_id, ScanStatusEnum.COMPLETED.value, update_end=True)
        else:
            # Run each check in parallel and finalize asynchronously using chord callback
            # Ensure regions are embedded in the payload snapshot passed to each task
            payload_with_regions = dict(payload)
            payload_with_regions["regions"] = regions
            job_group = group(
                execute_check_task.s(
                    scan_id,
                    service_id,
                    check_record["id"],
                    f"app.core.services.api_cloud_providers.{payload['cloud_provider_name']}.{service_name}.checks.{check_record['script_path']}",
                    payload_with_regions,
                    credentials,
                ) for check_record in checks
            )
            finalize = finalize_service_after_checks.s(scan_id, service_id, scan_service_id)
            chord(job_group)(finalize)

    result = loop.run_until_complete(run_task())
    # scan_service_task.request.acknowledge()
    return result


@celery_obj.task
def execute_check_task(scan_id, service_id, check_id, module_path, payload, credentials):
    import asyncio
    loop = asyncio.get_event_loop()

    async def run_check():
        # Log connection stats before getting pool
        try:
            stats = get_connection_stats()
            # logger.info(f"Connection stats before task: {stats['total_connections']}/{stats['aurora_connection_limit']} ({stats['utilization_percent']:.1f}%)")
        except Exception:
            pass  # Don't fail task if monitoring fails

        mysql_pool = await get_shared_mysql_pool()

        # Dynamic import of check module with clearer error context
        # module_path is expected to be a python module path like
        # "app.core.services.api_cloud_providers.aws.ec2.checks.ec2_ebs_encryption_check"
        try:
            module = importlib.import_module(module_path)
        except Exception as import_exc:
            await tracking_set_status(
                mysql_pool,
                scan_id,
                service_id,
                check_id,
                status="failed",
                error_message=f"Import error for {module_path}: {import_exc}",
                inc_retry=True,
                mark_dlq=False,
            )
            raise

        # Log regions context for each check as additional verification
        try:
            regions = payload.get("regions", [])
            logger.info("Executing check_id=%s for service_id=%s over regions: %s", check_id, service_id, ", ".join(regions) if regions else "<none>")
        except Exception:
            pass

        # Update tracking to running
        await tracking_set_status(mysql_pool, scan_id, service_id, check_id, status="running")

        # Expect a class `ComplianceCheck` or fallback to `Check`
        try:
            CheckClass = getattr(module, "ComplianceCheck")
        except AttributeError:
            try:
                CheckClass = getattr(module, "Check")
            except AttributeError as attr_exc:
                await tracking_set_status(
                    mysql_pool,
                    scan_id,
                    service_id,
                    check_id,
                    status="failed",
                    error_message=f"No ComplianceCheck/Check in {module_path}: {attr_exc}",
                    inc_retry=True,
                    mark_dlq=False,
                )
                raise
        account_id = str(payload.get("account_id"))
        check = CheckClass(credentials, payload.get("regions", []), account_id)
        try:
            findings = check.check()
            # Diagnostics for all services: summarize findings counts
            try:
                total_items = 0
                for k, v in findings.items():
                    details = v.get("details")
                    if isinstance(details, list):
                        total_items += len(details)
                logger.info(
                    "Check completed scan_id=%s service_id=%s check_id=%s items=%d",
                    scan_id,
                    service_id,
                    check_id,
                    total_items,
                )
            except Exception:
                pass
        except Exception as run_exc:
            await tracking_set_status(
                mysql_pool,
                scan_id,
                service_id,
                check_id,
                status="failed",
                error_message=f"Check run error in {module_path}: {run_exc}",
                inc_retry=True,
                mark_dlq=False,
            )
            raise

        # Save or update only the returned finding keys
        await save_or_update_findings(mysql_pool, scan_id, service_id, findings)

        # Mark completed
        await tracking_set_status(mysql_pool, scan_id, service_id, check_id, status="completed")

    try:
        return loop.run_until_complete(run_check())
    except Exception as exc:
        async def on_fail():
            mysql_pool = await get_shared_mysql_pool()
            await tracking_set_status(mysql_pool, scan_id, service_id, check_id, status="failed", error_message=str(exc), inc_retry=True)
        loop.run_until_complete(on_fail())
        raise


@celery_obj.task
def finalize_service_after_checks(results, scan_id, service_id, scan_service_id):
    import asyncio
    loop = asyncio.get_event_loop()

    async def finalize():
        mysql_pool = await get_shared_mysql_pool()
        await update_scan_service(conn_pool=mysql_pool, scan_service_id=scan_service_id, update_last_scanned_at=True)
        is_scan_completed = await check_scan_completion(mysql_pool, scan_id)
        if is_scan_completed["status_check"]:
            await update_scan(mysql_pool, scan_id, ScanStatusEnum.COMPLETED.value, update_end=True)

    return loop.run_until_complete(finalize())



# Email Verification Tasks
@celery_obj.task(bind=True, max_retries=3, default_retry_delay=60)
def send_otp_email_task(self, recipient_email: str, otp_code: str, first_name: str = None):
    """
    Celery task to send OTP email asynchronously (deprecated - use send_verification_email_task instead)

    Args:
        recipient_email: Email address of the recipient
        otp_code: 6-digit OTP code
        first_name: First name of the user (optional)
    """
    import asyncio

    async def send_email():
        try:
            from app.core.services.email_service import EmailService
            email_service = EmailService()
            success = await email_service.send_otp_email(recipient_email, otp_code, first_name)

            if success:
                logger.info(f"OTP email sent successfully to {recipient_email}")
                return {"status": "success", "message": "Email sent successfully"}
            else:
                logger.error(f"Failed to send OTP email to {recipient_email}")
                raise Exception("Email sending failed")

        except Exception as e:
            logger.error(f"Error sending OTP email to {recipient_email}: {str(e)}")
            raise self.retry(exc=e, countdown=60, max_retries=3)

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(send_email())
    finally:
        loop.close()


@celery_obj.task(bind=True, max_retries=3, default_retry_delay=60)
def send_verification_email_task(self, recipient_email: str, verification_url: str, first_name: str = None):
    """
    Celery task to send verification link email asynchronously

    Args:
        recipient_email: Email address of the recipient
        verification_url: Complete verification URL with token
        first_name: First name of the user (optional)
    """
    import asyncio

    async def send_email():
        try:
            from app.core.services.email_service import EmailService
            email_service = EmailService()
            success = await email_service.send_verification_email(recipient_email, verification_url, first_name)

            if success:
                logger.info(f"Verification email sent successfully to {recipient_email}")
                return {"status": "success", "message": "Verification email sent successfully"}
            else:
                logger.error(f"Failed to send verification email to {recipient_email}")
                raise Exception("Verification email sending failed")

        except Exception as e:
            logger.error(f"Error sending verification email to {recipient_email}: {str(e)}")
            raise self.retry(exc=e, countdown=60, max_retries=3)

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(send_email())
    finally:
        loop.close()


@celery_obj.task(bind=True)
def cleanup_expired_otps_task(self):
    """
    Celery task to clean up expired OTP records (deprecated - use cleanup_expired_verification_tokens_task instead)
    Runs periodically to maintain database cleanliness
    """
    import asyncio

    async def cleanup():
        try:
            from app.core.services.otp_service import OTPService
            otp_service = OTPService()
            cleaned_count = await otp_service.cleanup_expired_otps()

            logger.info(f"Cleaned up {cleaned_count} expired OTP records")
            return {"status": "success", "cleaned_count": cleaned_count}

        except Exception as e:
            logger.error(f"Error cleaning up expired OTPs: {str(e)}")
            return {"status": "error", "message": str(e)}

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(cleanup())
    finally:
        loop.close()


@celery_obj.task(bind=True)
def cleanup_expired_verification_tokens_task(self):
    """
    Celery task to clean up expired verification token records
    Runs periodically to maintain database cleanliness
    """
    import asyncio

    async def cleanup():
        try:
            from app.core.services.email_verification_service import EmailVerificationService
            verification_service = EmailVerificationService()
            cleaned_count = await verification_service.cleanup_expired_tokens()

            logger.info(f"Cleaned up {cleaned_count} expired verification token records")
            return {"status": "success", "cleaned_count": cleaned_count}

        except Exception as e:
            logger.error(f"Error cleaning up expired verification tokens: {str(e)}")
            return {"status": "error", "message": str(e)}

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(cleanup())
    finally:
        loop.close()


# Password Reset Tasks
@celery_obj.task(bind=True, max_retries=3, default_retry_delay=60)
def send_password_reset_email_task(self, recipient_email: str, reset_url: str, first_name: str = None):
    """
    Celery task to send password reset email asynchronously

    Args:
        recipient_email: Email address of the recipient
        reset_url: Complete password reset URL with token
        first_name: First name of the user (optional)
    """
    import asyncio

    async def send_email():
        try:
            from app.core.services.email_service import EmailService
            email_service = EmailService()
            success = await email_service.send_password_reset_email(recipient_email, reset_url, first_name)

            if success:
                logger.info(f"Password reset email sent successfully to {recipient_email}")
                return {"status": "success", "message": "Password reset email sent successfully"}
            else:
                logger.error(f"Failed to send password reset email to {recipient_email}")
                raise Exception("Password reset email sending failed")

        except Exception as e:
            logger.error(f"Error sending password reset email to {recipient_email}: {str(e)}")
            raise self.retry(exc=e, countdown=60, max_retries=3)

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(send_email())
    finally:
        loop.close()


@celery_obj.task(bind=True)
def cleanup_expired_password_reset_tokens_task(self):
    """
    Celery task to clean up expired password reset token records
    Runs periodically to maintain database cleanliness
    """
    import asyncio

    async def cleanup():
        try:
            from app.core.services.password_reset_service import PasswordResetService
            reset_service = PasswordResetService()
            cleaned_count = await reset_service.cleanup_expired_tokens()

            logger.info(f"Cleaned up {cleaned_count} expired password reset token records")
            return {"status": "success", "cleaned_count": cleaned_count}

        except Exception as e:
            logger.error(f"Error cleaning up expired password reset tokens: {str(e)}")
            return {"status": "error", "message": str(e)}

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(cleanup())
    finally:
        loop.close()


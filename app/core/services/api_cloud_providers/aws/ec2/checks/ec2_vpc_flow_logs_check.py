from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "vpc_flow_logs": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.VPC_FLOW_LOGS.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            vpcs_data = cached.get("vpcs") or {}
            flow_logs_data = cached.get("flow_logs") or {}

            # Get all VPCs
            vpcs = vpcs_data.get("Vpcs", [])

            region_findings = []
            non_compliant_vpcs = []

            for vpc in vpcs:
                vpc_id = vpc["VpcId"]

                # Check if Flow Logs are enabled for this VPC
                vpc_flow_logs = [
                    fl for fl in flow_logs_data.get("FlowLogs", [])
                    if fl.get("ResourceId") == vpc_id
                ]

                # If no Flow Logs exist for the VPC, mark as non-compliant
                flow_logs_enabled = len(vpc_flow_logs) > 0
                
                region_findings.append({
                    "vpc_id": vpc_id,
                    "region": region,
                    # "flow_logs_count": len(vpc_flow_logs),
                    "compliance": flow_logs_enabled
                })
                
                if not flow_logs_enabled:
                    non_compliant_vpcs.append(vpc_id)

            # Determine compliance status for this region
            if non_compliant_vpcs and findings["vpc_flow_logs"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["vpc_flow_logs"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["vpc_flow_logs"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass

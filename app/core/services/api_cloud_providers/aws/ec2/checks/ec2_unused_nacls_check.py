from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "unused_nacls": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.UNUSED_NACLS.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            nacls_data = cached.get("network_acls") or {}

            region_findings = []
            unused_nacls = []

            for nacl in nacls_data.get("NetworkAcls", []):
                nacl_id = nacl["NetworkAclId"]
                associated_subnets = nacl.get("Associations", [])

                # If no subnets are associated with the NACL, mark as unused
                is_unused = len(associated_subnets) == 0
                
                region_findings.append({
                    "nacl_id": nacl_id,
                    "region": region,
                    "associated_subnets_count": len(associated_subnets),
                    "compliance": not is_unused
                })
                
                if is_unused:
                    unused_nacls.append(nacl_id)

            # Determine compliance status for this region
            if unused_nacls and findings["unused_nacls"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["unused_nacls"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["unused_nacls"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass

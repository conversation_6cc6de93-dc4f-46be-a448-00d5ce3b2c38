from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, EKSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.eks.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "audit_logging_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.AUDIT_LOGGING_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            clusters = (cached.get("clusters") or {}).get("clusters", [])
            cluster_details = cached.get("cluster_details") or {}

            for cluster_arn in clusters:
                cluster_name = cluster_arn.split("/")[-1]
                cluster_detail = cluster_details.get(cluster_name, {}).get("cluster", {})
                
                logging_config = cluster_detail.get("logging", {}).get("clusterLogging", [])
                
                audit_logging_enabled = any(
                    log.get("types", []) == ["audit"] and log.get("enabled", False)
                    for log in logging_config
                )
                
                if findings["audit_logging_enabled"]["status"] == ResourceComplianceStatusEnum.PASS.value and not audit_logging_enabled:
                    findings["audit_logging_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["audit_logging_enabled"]["details"].append({
                    "cluster_name": cluster_name,
                    "region": region,
                    "compliance": audit_logging_enabled
                })

        return findings

    def remediate(self):
        pass

from typing import Dict, Any, List

from app.common import SeverityEnum, ResourceComplianceStatusEnum, EC2ChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.ec2.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "ec2_launch_templates_imdsv2": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EC2ChecksDescriptionEnum.EC2_LAUNCH_TEMPLATES_IMDSV2.value,
                "severity": SeverityEnum.LOW.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            launch_templates_data = cached.get("launch_templates") or {}
            launch_template_versions = cached.get("launch_template_versions") or {}

            region_findings = []
            non_compliant_templates = []

            for template in launch_templates_data.get("LaunchTemplates", []):
                template_id = template["LaunchTemplateId"]
                template_name = template["LaunchTemplateName"]

                # Check if IMDSv2 is enforced using cached template versions
                template_versions = launch_template_versions.get(template_id, {})
                latest_versions = template_versions.get("latest", {}).get("LaunchTemplateVersions", [])

                http_tokens_required = False
                if latest_versions:
                    latest_version = latest_versions[0]
                    metadata_options = latest_version.get("LaunchTemplateData", {}).get("MetadataOptions", {})
                    http_tokens_required = metadata_options.get("HttpTokens", "optional") == "required"

                region_findings.append({
                    "launch_template_id": template_id,
                    "launch_template_name": template_name,
                    "region": region,
                    "compliance": http_tokens_required
                })

                if not http_tokens_required:
                    non_compliant_templates.append(template_id)

            # Determine compliance status for this region
            if non_compliant_templates and findings["ec2_launch_templates_imdsv2"]["status"] == ResourceComplianceStatusEnum.PASS.value:
                findings["ec2_launch_templates_imdsv2"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["ec2_launch_templates_imdsv2"]["details"].extend(region_findings)

        return findings

    def remediate(self):
        pass

from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, RDSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.rds.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "rds_instance_supported_engine_version": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_SUPPORTED_ENGINE_VERSION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        # Define supported engines for version checking
        supported_engines = [
            "mysql", "mariadb", "postgres",  # Base engines
            "aurora-mysql", "aurora-postgresql"  # Aurora engines
        ]

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            instances = (cached.get("instances") or {}).get("DBInstances", [])
            clusters = (cached.get("clusters") or {}).get("DBClusters", [])
            engine_versions = cached.get("engine_versions", {})

            # Get all cluster member instance IDs to avoid duplicate checking
            cluster_member_ids = set()
            for cluster in clusters:
                for member in cluster.get("DBClusterMembers", []):
                    cluster_member_ids.add(member["DBInstanceIdentifier"])

            # Check standalone RDS instances (excluding cluster members)
            for instance in instances:
                db_instance_id = instance["DBInstanceIdentifier"]
                engine = instance["Engine"]
                engine_version = instance["EngineVersion"]

                # Skip cluster member instances - they will be handled in the cluster loop
                if db_instance_id in cluster_member_ids:
                    continue

                # Only check supported engines (MySQL, MariaDB, PostgreSQL, Aurora engines)
                if engine not in supported_engines:
                    continue

                # Get engine version information from cached AWS API data
                try:
                    engine_data = engine_versions.get(engine, {})
                    engine_version_list = engine_data.get("DBEngineVersions", [])

                    # Look for the specific version in the cached data
                    version_info = None
                    for version_data in engine_version_list:
                        if version_data.get("EngineVersion") == engine_version:
                            status = version_data.get("Status", "unknown")
                            version_info = {
                                "is_supported": status == "available",
                                "is_deprecated": status == "deprecated",
                                "status": status
                            }
                            break

                    # Version not found in cached data
                    if version_info is None:
                        version_info = {
                            "is_supported": False,
                            "is_deprecated": True,
                            "status": "not_found"
                        }

                except Exception:
                    # Default to considering it unsupported if there's an error
                    version_info = {
                        "is_supported": False,
                        "is_deprecated": True,
                        "status": "error"
                    }

                compliance_status = version_info["is_supported"] and not version_info["is_deprecated"]

                if (findings["rds_instance_supported_engine_version"]["status"] == ResourceComplianceStatusEnum.PASS.value
                        and not compliance_status):
                    findings["rds_instance_supported_engine_version"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["rds_instance_supported_engine_version"]["details"].append({
                    "db_instance_id": db_instance_id,
                    "engine": engine,
                    "engine_version": engine_version,
                    "region": region,
                    "compliance": compliance_status,
                })

            # Check cluster instances (Aurora and other cluster instances)
            for cluster in clusters:
                cluster_engine = cluster["Engine"]
                cluster_engine_version = cluster["EngineVersion"]

                # Only check Aurora engines (aurora-mysql, aurora-postgresql) and other supported engines
                if cluster_engine not in supported_engines:
                    continue

                # Get cluster members (instances in the cluster)
                cluster_members = cluster.get("DBClusterMembers", [])

                for member in cluster_members:
                    member_instance_id = member["DBInstanceIdentifier"]

                    # Find the full instance details from the instances list
                    member_instance = None
                    for instance in instances:
                        if instance["DBInstanceIdentifier"] == member_instance_id:
                            member_instance = instance
                            break

                    if member_instance is None:
                        # If we can't find the instance details, use cluster-level information
                        engine = cluster_engine
                        engine_version = cluster_engine_version
                    else:
                        # Use instance-level information if available
                        engine = member_instance["Engine"]
                        engine_version = member_instance["EngineVersion"]

                    # For Aurora engines, use the actual Aurora engine name, not the base engine
                    check_engine = engine

                    # Get engine version information from cached AWS API data
                    try:
                        engine_data = engine_versions.get(check_engine, {})
                        engine_version_list = engine_data.get("DBEngineVersions", [])

                        # Look for the specific version in the cached data
                        version_info = None
                        for version_data in engine_version_list:
                            if version_data.get("EngineVersion") == engine_version:
                                status = version_data.get("Status", "unknown")
                                version_info = {
                                    "is_supported": status == "available",
                                    "is_deprecated": status == "deprecated",
                                    "status": status
                                }
                                break

                        # Version not found in cached data
                        if version_info is None:
                            version_info = {
                                "is_supported": False,
                                "is_deprecated": True,
                                "status": "not_found"
                            }

                    except Exception:
                        # Default to considering it unsupported if there's an error
                        version_info = {
                            "is_supported": False,
                            "is_deprecated": True,
                            "status": "error"
                        }

                    compliance_status = version_info["is_supported"] and not version_info["is_deprecated"]

                    if (findings["rds_instance_supported_engine_version"]["status"] == ResourceComplianceStatusEnum.PASS.value
                            and not compliance_status):
                        findings["rds_instance_supported_engine_version"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["rds_instance_supported_engine_version"]["details"].append({
                        "db_instance_id": member_instance_id,
                        "engine": engine,
                        "engine_version": engine_version,
                        "region": region,
                        "compliance": compliance_status,
                    })

        return findings



    def remediate(self):
        pass

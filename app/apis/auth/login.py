from fastapi import Request, Response
from app import router
from app.common import LOGIN_API, SignUpLoginSchema, LoginResponseSchema, AUTH_TOKEN_TYPE, DefaultResponseSchema, EmailNotVerifiedException
from app.core.services.auth import LoginService


__all__ = ['login']


@router.post(LOGIN_API)
async def login(request: Request, response: Response, payload: SignUpLoginSchema):
    """
    User login endpoint with improved error handling for unverified users

    Returns:
        - LoginResponseSchema: On successful login
        - DefaultResponseSchema: On email not verified (with redirect info)
        - Error response: On invalid credentials or other errors
    """
    try:
        service = LoginService(payload)
        login_response = await service.process()

        return LoginResponseSchema(
            ok=True,
            status_code=200,
            token_type=AUTH_TOKEN_TYPE,
            access_token=login_response.get("access_token"),
            refresh_token=login_response.get("refresh_token"),
            message="User logged in successfully"
        )

    except EmailNotVerifiedException as e:
        from app import app
        app.logger.info(f"🚨 EmailNotVerifiedException caught in login API for: {payload.email}")
        response.status_code = 401
        response_data = DefaultResponseSchema(
            ok=False,
            status_code=401,
            message=str(e) or "Please verify your email address to continue",
            data={
                "error_type": "email_not_verified",
                "email": payload.email
            }
        )
        app.logger.info(f"🚨 Returning email verification response: {response_data.dict()}")
        return response_data

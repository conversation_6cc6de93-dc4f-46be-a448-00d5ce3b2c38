from datetime import datetime, timedelta
from app import app
from app.core.models.mysql import get_user_data, replace_refresh_token, update_user_last_login
from app.common import (InvalidCredentialsException, EmailNotVerifiedException, InternalServerException, get_encrypted_password,
                        create_access_token, create_refresh_token)


__all__ = ['LoginService']


class LoginService:
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool

    async def process(self):
        app.logger.info(f"🔍 Login attempt for email: {self.message.email}")
        user = await get_user_data(self.conn_pool, self.message)

        # If user doesn't exist in users table, check if they have a pending email verification
        if not user:
            app.logger.info(f"🔍 User not found in users table, checking verification status for: {self.message.email}")
            await self._check_pending_verification()
            # If we get here, user doesn't exist at all
            app.logger.info(f"🔍 No verification record found, treating as invalid credentials for: {self.message.email}")
            raise InvalidCredentialsException

        # Check password
        if user.get("password_hash") != get_encrypted_password(self.message.password):
            raise InvalidCredentialsException

        access_token = create_access_token(
            data={"sub": self.message.email, "user_id": user["id"], "workspace_id": user["workspace_id"]},
            expires_delta=timedelta(minutes=app.config.ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        refresh_token = create_refresh_token(
            data={"sub": self.message.email, "user_id": user["id"], "workspace_id": user["workspace_id"]},
            expires_delta=timedelta(days=app.config.REFRESH_TOKEN_EXPIRE_DAYS)
        )

        try:
            await replace_refresh_token(
                conn_pool=self.conn_pool,
                user_id=user.get("id"),
                refresh_token=refresh_token,
                expires_at=datetime.utcnow() + timedelta(days=app.config.REFRESH_TOKEN_EXPIRE_DAYS))

            # Update user's last_login on successful login
            await update_user_last_login(self.conn_pool, user.get("id"), datetime.utcnow())

            return {
                "access_token": access_token,
                "refresh_token": refresh_token
            }
        except Exception as error:
            app.logger.exception(f"Login error for user {self.message.email}: {error}")
            raise InternalServerException

    async def _check_pending_verification(self):
        """
        Check if user has a pending email verification
        Raises EmailNotVerifiedException if user exists in verification table (regardless of expiration)
        """
        from app.core.models.mysql import get_unverified_token_by_email_for_login

        try:
            # Check if there's an unverified token for this email (regardless of expiration)
            verification_record = await get_unverified_token_by_email_for_login(self.conn_pool, self.message.email)
            app.logger.info(f"🔍 Verification record for {self.message.email}: {verification_record}")

            if verification_record and not verification_record.get('is_verified', False):
                # User has signed up but hasn't verified their email (expired or not)
                app.logger.info(f"🚨 Found unverified user, raising EmailNotVerifiedException for: {self.message.email}")
                raise EmailNotVerifiedException(
                    f"Please verify your email address. Check your inbox for a verification link."
                )
        except EmailNotVerifiedException:
            # Re-raise the email verification exception
            raise
        except Exception as e:
            # Log any other errors but don't expose them
            app.logger.debug(f"Error checking verification status for {self.message.email}: {e}")
            # Don't raise - let the calling method handle as invalid credentials
